package com.wexl.retail.model;

import java.util.Date;

public interface OrgStudent {

  String getUsername();

  String getFirstName();

  String getLastName();

  String getSectionName();

  int getClassId();

  String getParentFirstName();

  String getParentLastName();

  String getParentEmail();

  String getParentMobileNumber();

  Date getDeletedAt();

  String getGuid();

  Date getLastLoginTime();

  int getBoardId();

  int getUserId();

  Long getStudentId();

  String getRollNumber();

  String getClassRollNumber();

  Boolean getFeeDefaulter();
}
