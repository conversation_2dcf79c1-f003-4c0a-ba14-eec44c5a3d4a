package com.wexl.erp.appointments.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.erp.appointments.model.AppointmentStatus;
import com.wexl.erp.appointments.model.AppointmentType;
import com.wexl.retail.guardian.model.GuardianRole;
import lombok.Builder;

public class GatePassDto {

  public record Request(
      @JsonProperty("gate_pass_date") Long gatePassDate,
      @JsonProperty("gate_pass_reason") String gatePassReason,
      @JsonProperty("gate_pass_type") AppointmentType gatePassType,
      @JsonProperty("pickup_person_name") String pickupPersonName,
      @JsonProperty("pickup_person_mobile") Long pickupPersonMobile,
      @JsonProperty("pickup_person_relation") GuardianRole pickupPersonRelation,
      @JsonProperty("recipient_name") String recipientName,
      @JsonProperty("teacher_auth_id") String teacherAuthId,
      @JsonProperty("role") String role) {}

  @Builder
  public record Response(
      @JsonProperty("gate_pass_id") Long gatePassId,
      @JsonProperty String guardianName,
      @JsonProperty String studentName,
      @JsonProperty String studentSection,
      @JsonProperty String gradeName,
      @JsonProperty String gradeSlug,
      @JsonProperty Long studentId,
      @JsonProperty Long guardianId,
      @JsonProperty("gate_pass_date") Long gatePassDate,
      @JsonProperty("gate_pass_reason") String gatePassReason,
      @JsonProperty("gate_pass_type") String gatePassType,
      @JsonProperty("pickup_person_name") String pickupPersonName,
      @JsonProperty("pickup_person_mobile") Long pickupPersonMobile,
      @JsonProperty("pickup_person_relation") GuardianRole pickupPersonRelation,
      @JsonProperty("recipient_name") String recipientName,
      @JsonProperty("role") String role,
      @JsonProperty("status") AppointmentStatus status,
      @JsonProperty("applied_date") Long appliedDate,
      @JsonProperty("account_holder") String accountHolder,
      @JsonProperty("reviewed_by") String reviewedBy,
      @JsonProperty("reviewed_on") Long reviewedOn,
      @JsonProperty("attachment_url") String attachmentUrl) {}

  public record ApprovalRequest(
      @JsonProperty("gate_pass_id") Long gatePassId,
      @JsonProperty("status") AppointmentStatus status) {}
}
