package com.wexl.chatbot.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record WhatsAppBotDto() {

  @Builder
  public record Msg91WebhookEvent(
      @JsonProperty("customer_name") String customerName,
      @JsonProperty("sender") String sender,
      @JsonProperty("integrated_number") String integratedNumber,
      @JsonProperty("company_id") Long companyId,
      @JsonProperty("content_type") String contentType,
      @JsonProperty("received_at") String receivedAt,
      @JsonProperty("replied_message_id") String repliedMessageId,
      @JsonProperty("message_uuid") String messageUuid,
      @JsonProperty("text") String text,
      @JsonProperty("contacts") List<Contacts> contacts,
      @JsonProperty("messages") List<Messages> messages,
      @JsonProperty("template_name") String templateName,
      @JsonProperty("template_language") String templateLanguage,
      @JsonProperty("new_password") String newPassword,
      @JsonProperty("url") String url,
      @JsonProperty("filename") String fileName) {}

  @Builder
  public record Messages(
      @JsonProperty("from") String from,
      @JsonProperty("id") String id,
      @JsonProperty("timestamp") String timestamp,
      @JsonProperty("text") Text text,
      @JsonProperty("type") String type,
      @JsonProperty("context") Context context,
      @JsonProperty("document") Document document) {}

  @Builder
  public record Document(
      @JsonProperty("filename") String filename,
      @JsonProperty("mime_type") String mimeType,
      @JsonProperty("sha256") String sha256,
      @JsonProperty("id") String id) {}

  @Builder
  public record Context(@JsonProperty("forwarded") Boolean forwarded) {}

  @Builder
  public record Contacts(
      @JsonProperty("wa_id") String mobile, @JsonProperty("profile") Profile profile) {}

  @Builder
  public record Profile(@JsonProperty("name") String name) {}

  @Builder
  public record Text(@JsonProperty("body") String body) {}

  public record Response(String status, boolean hasError, String errors, Data data) {}

  public record Data(String message_uuid, String message) {}

  @Builder
  public record CorrectionRequest(
      @JsonProperty("org_slug") String orgSlug,
      @JsonProperty("test_schedule_id") Long testScheduleId,
      @JsonProperty("pdf_url") String pdfUrl) {}
}
