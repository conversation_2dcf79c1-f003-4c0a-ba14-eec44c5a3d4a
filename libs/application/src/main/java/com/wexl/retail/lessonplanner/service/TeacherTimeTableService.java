package com.wexl.retail.lessonplanner.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.model.Entity;
import com.wexl.retail.lessonplanner.dto.TeacherTimeTableDto;
import com.wexl.retail.lessonplanner.model.LessonPlanner;
import com.wexl.retail.lessonplanner.model.TeacherTimeTable;
import com.wexl.retail.lessonplanner.model.TeacherTimeTableDetail;
import com.wexl.retail.lessonplanner.repository.*;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.TeacherSubjects;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.repository.TeacherSubjectsRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.StrapiService;
import java.sql.Timestamp;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TeacherTimeTableService {
  private final DateTimeUtil dateTimeUtil;
  private final TeacherTimeTableRepository teacherTimeTableRepository;
  private final TeacherTimeTableDetailsRepository teacherTimeTableDetailRepository;
  private final TeacherRepository teacherRepository;
  private final SectionRepository sectionRepository;
  private final TeacherSubjectsRepository teacherSubjectsRepository;
  private final PeriodTableRepository periodTableRepository;
  private final StrapiService strapiService;
  private final LessonPlannerRepository lessonPlannerRepository;
  private final UserService userService;
  private final UserRepository userRepository;
  private final AuthService authService;

  public void createTimeTable(String orgSlug, TeacherTimeTableDto.TimeTableRequest request) {
    validateTimeTableRequest(request);
    var existingTeacherTimeTable =
        teacherTimeTableRepository.findByOrgSlugAndBoardSlugAndGradeSlugAndSectionUuid(
            orgSlug, request.boardSlug(), request.gradeSlug(), request.sectionUuid());
    if (existingTeacherTimeTable.isPresent()) {
      existingTeacherTimeTable
          .get()
          .setTeacherTimeTableDetails(
              buildLessonPlannerDetails(existingTeacherTimeTable.get(), request));
      teacherTimeTableRepository.save(existingTeacherTimeTable.get());
    } else {
      TeacherTimeTable teacherTimeTable = new TeacherTimeTable();
      teacherTimeTable.setOrgSlug(orgSlug);
      teacherTimeTable.setBoardSlug(request.boardSlug());
      teacherTimeTable.setGradeSlug(request.gradeSlug());
      teacherTimeTable.setSectionUuid(request.sectionUuid());
      teacherTimeTable.setTeacherTimeTableDetails(
          buildLessonPlannerDetails(teacherTimeTable, request));
      teacherTimeTableRepository.save(teacherTimeTable);
    }
  }

  private void validateTimeTableRequest(TeacherTimeTableDto.TimeTableRequest request) {
    for (TeacherTimeTableDto.PeriodsRequest periodRequest : request.periodsRequest()) {
      boolean isSubjectEmpty =
          periodRequest.subjectSlug() == null || periodRequest.subjectSlug().trim().isEmpty();
      boolean isTeacherInvalid =
          periodRequest.teacherId() == null || periodRequest.teacherId() == 0;
      boolean isPeriodNumberInvalid =
          periodRequest.periodNumber() == null || periodRequest.periodNumber() == 0;

      if (isSubjectEmpty && isTeacherInvalid) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, "Please select teacher and subject.");
      }
      if (isSubjectEmpty) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Please select subject.");
      }
      if (isTeacherInvalid) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Please select teacher.");
      }
      if (isPeriodNumberInvalid) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, "Please select proper period number.");
      }
    }
  }

  private List<TeacherTimeTableDetail> buildLessonPlannerDetails(
      TeacherTimeTable teacherTimeTable, TeacherTimeTableDto.TimeTableRequest request) {
    List<TeacherTimeTableDetail> teacherTimeTableDetails = new ArrayList<>();
    LocalDateTime date = dateTimeUtil.convertEpochToIso8601(request.date());
    request
        .periodsRequest()
        .forEach(
            periodRequest -> {
              if (periodRequest.id() != null) {
                updateTeacherTimeTableDetailById(periodRequest);
              } else {
                TeacherTimeTableDetail teacherTimeTableDetail = new TeacherTimeTableDetail();
                var teacher = teacherRepository.findById(periodRequest.teacherId()).orElseThrow();
                var section =
                    sectionRepository
                        .findByUuid(UUID.fromString(teacherTimeTable.getSectionUuid()))
                        .orElseThrow();
                TeacherSubjects teacherSubject = null;
                var existingTeacherSubject =
                    teacherSubjectsRepository.findFirstByTeacherAndSectionAndSubjectAndBoard(
                        teacher, section, periodRequest.subjectSlug(), periodRequest.board());
                if (existingTeacherSubject.isEmpty()
                    && UserRoleHelper.get().isOrgAdmin(teacher.getUserInfo())) {
                  TeacherSubjects teacherSubjects = new TeacherSubjects();
                  teacherSubjects.setCreatedAt(Timestamp.valueOf(LocalDateTime.now()));
                  teacherSubjects.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
                  teacherSubjects.setTeacher(teacher);
                  teacherSubjects.setSection(section);
                  teacherSubjects.setBoard(periodRequest.board());
                  teacherSubjects.setSubject(periodRequest.subjectSlug());
                  teacherSubject = teacherSubjectsRepository.save(teacherSubjects);
                } else if (existingTeacherSubject.isPresent()) {
                  teacherSubject = existingTeacherSubject.get();
                } else {
                  throw new ApiException(
                      InternalErrorCodes.INVALID_REQUEST, "error.SubjectNotMapped");
                }
                var startTime = dateTimeUtil.convertEpochToIso8601Legacy(periodRequest.startTime());
                var endTime = dateTimeUtil.convertEpochToIso8601Legacy(periodRequest.endTime());
                var period = periodTableRepository.findById(periodRequest.periodNumber());
                teacherTimeTableDetail.setDate(date.toLocalDate());
                teacherTimeTableDetail.setTeacherSubject(teacherSubject);
                teacherTimeTableDetail.setDayOfWeek(date.getDayOfWeek());
                teacherTimeTableDetail.setStartTime(startTime);
                teacherTimeTableDetail.setEndTime(endTime);
                teacherTimeTableDetail.setRoomNumber(periodRequest.roomNumber());
                teacherTimeTableDetail.setTeacherTimeTable(teacherTimeTable);
                teacherTimeTableDetail.setPeriodTable(period.get());
                teacherTimeTableDetails.add(teacherTimeTableDetail);
              }
            });
    return teacherTimeTableDetails;
  }

  public TeacherTimeTableDto.TimeTableResponse getTimeTable(
      String orgSlug, String boardSlug, String gradeSlug, String sectionUuid, Boolean isStudent) {
    var teacherTimeTable =
        teacherTimeTableRepository.findByOrgSlugAndBoardSlugAndGradeSlugAndSectionUuid(
            orgSlug, boardSlug, gradeSlug, sectionUuid);
    return teacherTimeTable
        .map(
            timeTable ->
                TeacherTimeTableDto.TimeTableResponse.builder()
                    .teacherTimeTableId(timeTable.getId())
                    .boardSlug(timeTable.getBoardSlug())
                    .gradeSlug(timeTable.getGradeSlug())
                    .sectionUuid(timeTable.getSectionUuid())
                    .dateResponse(buildTeacherTimeTableDetailsResponse(timeTable, isStudent))
                    .build())
        .orElse(null);
  }

  public List<LocalDate> getCurrentWeekDates() {
    LocalDate startDate = LocalDate.of(2025, 3, 1);
    LocalDate endDate = LocalDate.now().plusDays(90);
    if (endDate.isBefore(startDate)) {
      return Collections.emptyList();
    }
    return dateTimeUtil.getDates(startDate, endDate);
  }

  private List<TeacherTimeTableDto.DateResponse> buildTeacherTimeTableDetailsResponse(
      TeacherTimeTable teacherTimeTable, Boolean isStudent) {
    var currentWeekDates = getCurrentWeekDates();
    List<TeacherTimeTableDetail> teacherTimeTableDetails;
    if (isStudent) {
      teacherTimeTableDetails =
          teacherTimeTableDetailRepository.findAllByTeacherTimeTableAndDateIn(
              teacherTimeTable, currentWeekDates);
    } else {
      var user = authService.getTeacherDetails();
      if (UserRoleHelper.get().isOrgAdmin(user)) {
        teacherTimeTableDetails =
            teacherTimeTableDetailRepository.findAllByTeacherTimeTableAndDateIn(
                teacherTimeTable, currentWeekDates);
      } else {
        var teacherSubjects = teacherSubjectsRepository.findAllByTeacher(user.getTeacherInfo());
        teacherTimeTableDetails =
            teacherTimeTableDetailRepository.findAllByTeacherTimeTableAndDateInAndTeacherSubjectIn(
                teacherTimeTable, currentWeekDates, teacherSubjects);
      }
    }
    return buildTeacherTimeTableDetails(currentWeekDates, teacherTimeTableDetails);
  }

  private List<TeacherTimeTableDto.DateResponse> buildTeacherTimeTableDetails(
      List<LocalDate> currentWeekDates, List<TeacherTimeTableDetail> teacherTimeTableDetails) {
    List<TeacherTimeTableDto.DateResponse> datesResponse = new ArrayList<>();
    var dateToDetailsMap = new HashMap<Long, List<TeacherTimeTableDetail>>();
    var allSubjects = strapiService.getAllSubjects();
    var allEduBoards = strapiService.getAllBoards();
    Map<String, String> subjectMap =
        allSubjects.stream().collect(Collectors.toMap(Entity::getSlug, Entity::getName));
    Map<String, String> boardMap =
        allEduBoards.stream().collect(Collectors.toMap(Entity::getSlug, Entity::getAssetName));

    for (LocalDate date : currentWeekDates) {
      Long epochDate = convertIso8601ToEpoch(date.atStartOfDay());
      dateToDetailsMap.put(epochDate, new ArrayList<>());
    }

    var detailsByDate =
        teacherTimeTableDetails.stream()
            .collect(
                Collectors.groupingBy(
                    detail -> convertIso8601ToEpoch(detail.getDate().atStartOfDay())));

    dateToDetailsMap.putAll(detailsByDate);

    dateToDetailsMap.forEach(
        (date, detailsForDate) -> {
          List<TeacherTimeTableDto.PeriodsResponse> periodsResponses = new ArrayList<>();

          if (!detailsForDate.isEmpty()) {
            detailsForDate.forEach(
                detail -> {
                  var section =
                      sectionRepository
                          .findByUuid(
                              UUID.fromString(detail.getTeacherTimeTable().getSectionUuid()))
                          .orElseThrow();
                  Optional<LessonPlanner> lessonPlanner =
                      lessonPlannerRepository.findByTeacherTimeTableDetail(detail);

                  periodsResponses.add(
                      TeacherTimeTableDto.PeriodsResponse.builder()
                          .id(detail.getId())
                          .gradeName(section.getGradeName())
                          .gradeSlug(section.getGradeSlug())
                          .boardName(boardMap.get(detail.getTeacherTimeTable().getBoardSlug()))
                          .boardSlug(detail.getTeacherTimeTable().getBoardSlug())
                          .sectionName(section.getName())
                          .teacherSubjectId(detail.getTeacherSubject().getId())
                          .subjectSlug(detail.getTeacherSubject().getSubject())
                          .subjectName(subjectMap.get(detail.getTeacherSubject().getSubject()))
                          .teacherId(detail.getTeacherSubject().getTeacher().getId())
                          .teacherName(
                              userService.getNameByUserInfo(
                                  detail.getTeacherSubject().getTeacher().getUserInfo()))
                          .startTime(convertIso8601ToEpoch(detail.getStartTime()))
                          .endTime(convertIso8601ToEpoch(detail.getEndTime()))
                          .roomNumber(detail.getRoomNumber())
                          .periodNumber(detail.getPeriodTable().getId())
                          .lessonPlannerId(lessonPlanner.map(LessonPlanner::getId).orElse(null))
                          .lessonPlannerFields(lessonPlanner.map(this::buildFields).orElse(null))
                          .build());
                });
          }

          LocalDate localDate =
              currentWeekDates.stream()
                  .filter(d -> convertIso8601ToEpoch(d.atStartOfDay()).equals(date))
                  .findFirst()
                  .orElse(null);

          DayOfWeek dayOfWeek =
              localDate != null
                  ? localDate.getDayOfWeek()
                  : (!detailsForDate.isEmpty() ? detailsForDate.getFirst().getDayOfWeek() : null);

          datesResponse.add(
              TeacherTimeTableDto.DateResponse.builder()
                  .date(date)
                  .dayOfWeek(dayOfWeek)
                  .teacherTimeTableDetails(
                      periodsResponses.stream()
                          .sorted(
                              Comparator.comparing(TeacherTimeTableDto.PeriodsResponse::startTime))
                          .toList())
                  .build());
        });

    return datesResponse.stream()
        .sorted(Comparator.comparing(TeacherTimeTableDto.DateResponse::date))
        .toList();
  }

  private List<TeacherTimeTableDto.LessonPlannerFields> buildFields(LessonPlanner lessonPlanner) {
    return lessonPlanner.getLessonPlannerDetails().stream()
        .map(
            detail ->
                TeacherTimeTableDto.LessonPlannerFields.builder()
                    .fliedId(detail.getLessonPlannerTemplateField().getId())
                    .fieldName(detail.getLessonPlannerTemplateField().getFieldName())
                    .definition(detail.getDefinition())
                    .build())
        .toList();
  }

  public void updateTimeTable(TeacherTimeTableDto.PeriodsRequestBulk request) {
    request.periodsRequest().forEach(this::updateTeacherTimeTableDetailById);
  }

  private void updateTeacherTimeTableDetailById(TeacherTimeTableDto.PeriodsRequest periodRequest) {
    var teacherTimeTableDetail =
        teacherTimeTableDetailRepository
            .findById(periodRequest.id())
            .orElseThrow(() -> new RuntimeException("error.TeacherTimeTableDetailNotFound"));
    var teacher = teacherRepository.findById(periodRequest.teacherId()).orElseThrow();
    var section =
        sectionRepository
            .findByUuid(
                UUID.fromString(teacherTimeTableDetail.getTeacherTimeTable().getSectionUuid()))
            .orElseThrow();
    var teacherSubject =
        teacherSubjectsRepository.findFirstByTeacherAndSectionAndSubjectAndBoard(
            teacher, section, periodRequest.subjectSlug(), periodRequest.board());
    teacherTimeTableDetail.setTeacherSubject(teacherSubject.get());
    teacherTimeTableDetail.setStartTime(
        dateTimeUtil.convertEpochToIso8601Legacy(periodRequest.startTime()));
    teacherTimeTableDetail.setEndTime(
        dateTimeUtil.convertEpochToIso8601Legacy(periodRequest.endTime()));
    teacherTimeTableDetail.setRoomNumber(periodRequest.roomNumber());
    teacherTimeTableDetailRepository.save(teacherTimeTableDetail);
  }

  public void deleteTimeTable(Long teacherTimeTableDetailId) {
    teacherTimeTableDetailRepository.deleteById(teacherTimeTableDetailId);
  }

  public TeacherTimeTableDetail validateTeacherTimeTableDetail(Long teacherTimeTableDetailId) {
    return teacherTimeTableDetailRepository
        .findById(teacherTimeTableDetailId)
        .orElseThrow(() -> new RuntimeException("error.TeacherTimeTableDetailNotFound"));
  }

  public List<TeacherTimeTableDto.DateResponse> getTeacherTimeTable(Long teacherId) {
    var teacher = teacherRepository.findById(teacherId).orElseThrow();
    var teacherSubjects = teacherSubjectsRepository.findAllByTeacher(teacher);
    var currentWeekDates = getCurrentWeekDates();
    var teacherTimeTableDetails =
        teacherTimeTableDetailRepository.findAllByTeacherSubjectInAndDateIn(
            teacherSubjects, currentWeekDates);
    if (teacherTimeTableDetails.isEmpty()) {
      return new ArrayList<>();
    }
    return buildTeacherTimeTableDetails(currentWeekDates, teacherTimeTableDetails);
  }

  public TeacherTimeTableDto.TimeTableResponse getStudentTimeTable(
      String orgSlug, String studentAuthId) {
    var user = userRepository.findByAuthUserId(studentAuthId).orElseThrow();
    var section = user.getStudentInfo().getSection();
    return getTimeTable(
        orgSlug,
        section.getBoardSlug(),
        section.getGradeSlug(),
        section.getUuid().toString(),
        true);
  }

  public void cloneTimeTable(TeacherTimeTableDto.CloneRequest request) {
    if (request.weeklyClone()) {
      weeklyClone(request);
    } else {
      var teacherTimeTable =
          teacherTimeTableRepository
              .findById(request.timeTableId())
              .orElseThrow(() -> new RuntimeException("error.TeacherTimeTableNotFound"));

      LocalDate sourceFromDate =
          dateTimeUtil.convertEpochToIso8601(request.sourceFromDate()).toLocalDate();
      LocalDate sourceToDate =
          dateTimeUtil.convertEpochToIso8601(request.sourceToDate()).toLocalDate();
      LocalDate targetFromDate =
          dateTimeUtil.convertEpochToIso8601(request.targetFromDate()).toLocalDate();
      LocalDate targetToDate =
          dateTimeUtil.convertEpochToIso8601(request.targetToDate()).toLocalDate();

      long sourceDateDiff = ChronoUnit.DAYS.between(sourceFromDate, sourceToDate);
      long targetDateDiff = ChronoUnit.DAYS.between(targetFromDate, targetToDate);
      if (sourceDateDiff != targetDateDiff) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.DateRangeMismatch");
      }

      List<TeacherTimeTableDetail> sourceDetails =
          teacherTimeTableDetailRepository.findAllByTeacherTimeTableAndDateBetween(
              teacherTimeTable, sourceFromDate, sourceToDate);

      if (sourceDetails.isEmpty()) {
        throw new RuntimeException("error.NoTimeTableDetailsFoundForSourceDates");
      }

      long daysDifference = ChronoUnit.DAYS.between(sourceFromDate, targetFromDate);

      List<TeacherTimeTableDetail> newDetails = new ArrayList<>();

      for (TeacherTimeTableDetail sourceDetail : sourceDetails) {

        LocalDate sourceDate = sourceDetail.getDate();
        LocalDate targetDate = sourceDate.plusDays(daysDifference);

        if (!targetDate.isBefore(targetFromDate) && !targetDate.isAfter(targetToDate)) {
          TeacherTimeTableDetail newDetail = new TeacherTimeTableDetail();
          newDetail.setTeacherTimeTable(teacherTimeTable);
          newDetail.setTeacherSubject(sourceDetail.getTeacherSubject());
          newDetail.setDate(targetDate);
          newDetail.setDayOfWeek(targetDate.getDayOfWeek());
          newDetail.setStartTime(sourceDetail.getStartTime().plusDays(daysDifference));
          newDetail.setEndTime(sourceDetail.getEndTime().plusDays(daysDifference));
          newDetail.setRoomNumber(sourceDetail.getRoomNumber());
          newDetail.setPeriodTable(sourceDetail.getPeriodTable());

          newDetails.add(newDetail);
        }
      }

      teacherTimeTableDetailRepository.saveAll(newDetails);
    }
  }

  private void weeklyClone(TeacherTimeTableDto.CloneRequest request) {
    var teacherTimeTable =
        teacherTimeTableRepository
            .findById(request.timeTableId())
            .orElseThrow(() -> new RuntimeException("error.TeacherTimeTableNotFound"));

    LocalDate sourceFromDate =
        dateTimeUtil.convertEpochToIso8601(request.sourceFromDate()).toLocalDate();
    LocalDate sourceToDate =
        dateTimeUtil.convertEpochToIso8601(request.sourceToDate()).toLocalDate();
    LocalDate targetFromDate =
        dateTimeUtil.convertEpochToIso8601(request.targetFromDate()).toLocalDate();
    LocalDate targetToDate =
        dateTimeUtil.convertEpochToIso8601(request.targetToDate()).toLocalDate();

    List<LocalDate> targetDates = dateTimeUtil.getDates(targetFromDate, targetToDate);

    List<TeacherTimeTableDetail> sourceDetails;
    var user = authService.getTeacherDetails();
    if (UserRoleHelper.get().isOrgAdmin(user)) {
      sourceDetails =
          teacherTimeTableDetailRepository.findAllByTeacherTimeTableAndDateBetween(
              teacherTimeTable, sourceFromDate, sourceToDate);
    } else {
      sourceDetails =
          teacherTimeTableDetailRepository
              .findAllByTeacherTimeTableAndDateBetweenAndTeacherSubject_Teacher(
                  teacherTimeTable, sourceFromDate, sourceToDate, user.getTeacherInfo());
    }
    if (sourceDetails.isEmpty()) {
      throw new RuntimeException("error.NoTimeTableDetailsFoundForSourceDates");
    }

    // Map: DayOfWeek -> List<TeacherTimeTableDetail> from source
    Map<DayOfWeek, List<TeacherTimeTableDetail>> sourceByDayOfWeek =
        sourceDetails.stream().collect(Collectors.groupingBy(TeacherTimeTableDetail::getDayOfWeek));

    // Map: DayOfWeek -> List<LocalDate> from target
    Map<DayOfWeek, List<LocalDate>> targetByDayOfWeek =
        targetDates.stream().collect(Collectors.groupingBy(LocalDate::getDayOfWeek));

    List<TeacherTimeTableDetail> newDetails = new ArrayList<>();

    for (DayOfWeek dayOfWeek : sourceByDayOfWeek.keySet()) {
      List<TeacherTimeTableDetail> detailsForDay = sourceByDayOfWeek.get(dayOfWeek);
      List<LocalDate> targetDays =
          targetByDayOfWeek.getOrDefault(dayOfWeek, Collections.emptyList());

      for (LocalDate targetDate : targetDays) {
        for (TeacherTimeTableDetail sourceDetail : detailsForDay) {
          TeacherTimeTableDetail newDetail = new TeacherTimeTableDetail();
          newDetail.setTeacherTimeTable(teacherTimeTable);
          newDetail.setTeacherSubject(sourceDetail.getTeacherSubject());
          newDetail.setDate(targetDate);
          newDetail.setDayOfWeek(dayOfWeek);
          newDetail.setStartTime(targetDate.atTime(sourceDetail.getStartTime().toLocalTime()));
          newDetail.setEndTime(targetDate.atTime(sourceDetail.getEndTime().toLocalTime()));
          newDetail.setRoomNumber(sourceDetail.getRoomNumber());
          newDetail.setPeriodTable(sourceDetail.getPeriodTable());
          newDetails.add(newDetail);
        }
      }
    }

    teacherTimeTableDetailRepository.saveAll(newDetails);
  }
}
