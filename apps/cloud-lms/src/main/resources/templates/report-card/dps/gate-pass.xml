<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format"
         xmlns:th="http://www.thymeleaf.org">

    <fo:layout-master-set>
        <fo:simple-page-master master-name="gate-pass"
                               page-height="29.7cm" page-width="21cm"
                               margin="1.5cm">
            <fo:region-body/>
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="gate-pass">
        <fo:flow flow-name="xsl-region-body">

            <fo:block-container width="100%" padding="10pt" border="1pt solid #b0bec5">

                <!-- Header: Logo, Title, Address -->
                <fo:table width="100%" table-layout="fixed" margin-bottom="8pt">
                    <fo:table-column column-width="25%"/>
                    <fo:table-column column-width="50%"/>
                    <fo:table-column column-width="25%"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block th:if="${model.schoolLogo != null}">
                                    <fo:external-graphic th:src="'url(' + @{${model.schoolLogo}} + ')'" content-width="45mm"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell text-align="center" display-align="center">
                                <fo:block font-size="22pt" font-weight="bold" color="#01579b" font-family="Arial, sans-serif">
                                    <fo:block th:text="${model.schoolName}">SCHOOL NAME</fo:block>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell text-align="right">
                                <fo:block font-size="9pt" color="#616161">
                                    Address : Mohali, Punjab<br/>Phone : 8697972739
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <!-- Title -->
                <fo:block text-align="center" font-size="20pt" font-weight="bold" color="#0277bd" space-after="10pt">
                    GATE PASS
                </fo:block>

                <!-- Gate Pass Info -->
                <fo:table width="100%" border="1pt solid #b0bec5" table-layout="fixed" space-after="8pt">
                    <fo:table-column column-width="50%"/>
                    <fo:table-column column-width="50%"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell padding="6pt" border-right="1pt solid #b0bec5">
                                <fo:block font-weight="bold">Gate Pass No:</fo:block>
                                <fo:block th:text="${model.gatePassNumber}"/>
                            </fo:table-cell>
                            <fo:table-cell padding="6pt">
                                <fo:block font-weight="bold">Date:</fo:block>
                                <fo:block th:text="${model.gatePassDate}"/>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <!-- Student Info -->
                <fo:block font-size="14pt" font-weight="bold" color="#0288d1" space-after="5pt">Student Information</fo:block>
                <fo:table width="100%" border="1pt solid #b0bec5" table-layout="fixed" space-after="8pt">
                    <fo:table-column column-width="25%"/>
                    <fo:table-column column-width="25%"/>
                    <fo:table-column column-width="25%"/>
                    <fo:table-column column-width="25%"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell padding="5pt"><fo:block font-weight="bold">Name:</fo:block></fo:table-cell>
                            <fo:table-cell padding="5pt"><fo:block th:text="${model.studentName}"/></fo:table-cell>
                            <fo:table-cell padding="5pt"><fo:block font-weight="bold">Class:</fo:block></fo:table-cell>
                            <fo:table-cell padding="5pt"><fo:block th:text="${model.className}"/></fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell padding="5pt"><fo:block font-weight="bold">Section:</fo:block></fo:table-cell>
                            <fo:table-cell padding="5pt"><fo:block th:text="${model.sectionName}"/></fo:table-cell>
                            <fo:table-cell padding="5pt"><fo:block font-weight="bold">Roll No:</fo:block></fo:table-cell>
                            <fo:table-cell padding="5pt"><fo:block th:text="${model.rollNumber}"/></fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <!-- Gate Pass Request -->
                <fo:block font-size="14pt" font-weight="bold" color="#0288d1" space-after="5pt">Gate Pass Request</fo:block>
                <fo:table width="100%" border="1pt solid #b0bec5" table-layout="fixed" space-after="8pt">
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell padding="6pt">
                                <fo:block><fo:inline font-weight="bold">Reason:</fo:inline>
                                    <fo:inline th:text="${model.reason}"/>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell padding="6pt">
                                <fo:block><fo:inline font-weight="bold">Type:</fo:inline>
                                    <fo:inline th:text="${model.type}"/>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <!-- Approval Details -->
                <fo:block font-size="14pt" font-weight="bold" color="#0288d1" space-after="5pt">Approval Details</fo:block>
                <fo:table width="100%" border="1pt solid #b0bec5" table-layout="fixed" space-after="8pt">
                    <fo:table-column column-width="50%"/>
                    <fo:table-column column-width="50%"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell padding="5pt">
                                <fo:block><fo:inline font-weight="bold">Status:</fo:inline>
                                    <fo:inline th:text="${model.status}" color="#2e7d32"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding="5pt">
                                <fo:block><fo:inline font-weight="bold">Approved By:</fo:inline>
                                    <fo:inline th:text="${model.approvedBy}"/>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell padding="5pt">
                                <fo:block><fo:inline font-weight="bold">Applied Date:</fo:inline>
                                    <fo:inline th:text="${model.appliedDate}"/>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding="5pt">
                                <fo:block><fo:inline font-weight="bold">Approval Date:</fo:inline>
                                    <fo:inline th:text="${model.approvalDate}"/>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <!-- Pickup Person -->
                <fo:table width="100%" border="1pt solid #b0bec5" table-layout="fixed" space-after="12pt">
                    <fo:table-column column-width="33%"/>
                    <fo:table-column column-width="33%"/>
                    <fo:table-column column-width="34%"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell padding="5pt"><fo:block font-weight="bold">Person coming to fetch the child</fo:block></fo:table-cell>
                            <fo:table-cell padding="5pt"><fo:block th:text="${model.pickupPersonName}"/></fo:table-cell>
                            <fo:table-cell padding="5pt"><fo:block th:text="${model.pickupPersonRelation}"/></fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <!-- Signature -->
                <fo:block text-align="right" font-size="10pt" font-weight="bold">Signature</fo:block>

            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>