package com.wexl.retail.repository;

import com.wexl.retail.model.OrgStudent;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.student.studentdata.SkilliomaStudent;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface StudentRepository extends JpaRepository<Student, Long> {

  @Query(
      value =
          """
                  select s.* from students s
                  inner join users u on u.id=s.user_id
                  where s.id=:id and u.organization=:orgSlug
                  """,
      nativeQuery = true)
  Student getStudentByIdAndOrgSlug(long id, String orgSlug);

  @Query(
      value =
          """
                  select s.id from students s
                  inner join users u on u.id=s.user_id
                  where u.auth_user_id in (:authUserIds) and u.organization=:orgSlug
                  """,
      nativeQuery = true)
  List<Long> getStudentIdsByAuthUserId(List<String> authUserIds, String orgSlug);

  @Query(
      value =
          """
          select s.* from students s
          inner join users u on u.id=s.user_id
          where u.auth_user_id=:authUserID and u.organization=:orgSlug
          """,
      nativeQuery = true)
  Student getStudentByAuthUserIdAndOrgSlug(String authUserID, String orgSlug);

  @Query("SELECT p FROM Student p LEFT JOIN p.section WHERE p.userInfo = (:userInfo)")
  Optional<Student> findByUserInfo(User userInfo);

  @Query(
      value =
          """
                  select u.id as userId, s.id as studentId, u.user_name as username, u.first_name as firstName,
                                        u.last_name as lastName, s.class_id as classId,
                                         sec.name as sectionName,
                                        s.board_id as boardId,
                                        u.deleted_at as deletedAt,u.last_login as lastLoginTime ,u.guid,
                                        gs.first_name AS parentFirstName,
                                        gs.last_name AS parentLastName,
                                        gs.mobile_number AS parentMobileNumber,
                                        gs.email AS parentEmail,
                                        s.class_roll_number as classRollNumber,
                                        s.roll_number as rollNumber ,s.fee_defaulter as feeDefaulter
                                        from students s
                                        inner join users u on s.user_id = u.id
                                        inner join sections sec on sec.id = s.section_id
                                        left join guardians gs on gs.student_id=s.id and gs.is_primary is true
                                        where u.organization=:organizationSlug
                                        and (cast((:gradeId) as varChar) is null or s.class_id in (:gradeId)) and u.deleted_at is null
                                        order by s.created_at desc
                  """,
      nativeQuery = true)
  List<OrgStudent> findAllStudents(String organizationSlug, Long gradeId);

  @Query(
      value =
          """
            SELECT s.*
            FROM students s
            INNER JOIN users u ON s.user_id = u.id
              WHERE u.organization = :orgId
              AND (CAST(:boardId AS VARCHAR) IS NULL OR s.board_id IN (:boardId))
              AND (CAST(:gradeId AS VARCHAR) IS NULL OR s.class_id IN (:gradeId))
              AND u.deleted_at is null
              order by s.class_roll_number ASC NULLS LAST
            """,
      nativeQuery = true)
  List<Student> getStudentsByOrgSlugAndBoardAndGrade(
      String orgId, Long boardId, List<Long> gradeId);

  @Query("SELECT s FROM Student s WHERE s.section IN :sections")
  List<Student> getListStudentsBySections(@Param("sections") List<Section> sections);

  @Query(
      value =
          """
            select std.*
            from students std
                    inner join users u on u.id = std.user_id
            where std.class_id=:classId  and u.organization = :orgSlug
              and std.deleted_at is null and u.deleted_at is null
                      """,
      nativeQuery = true)
  List<Student> findStudentsByClassAndOrg(int classId, String orgSlug);

  @Query(value = "select s.* from students s where s.user_id = :userId", nativeQuery = true)
  Student findByUserId(long userId);

  List<Student> getStudentsBySection(Section section);

  boolean existsByClassRollNumberAndSection(String rollNumber, Section section);

  @Query(
      value =
          """
                      select std.* from students std
                       inner join sections sec on sec.id = std.section_id
                       inner join users u on u.id = std.user_id
                       where grade_slug in(:gradeSlug)  and sec.organization = :orgSlug
                       and std.deleted_at is null and  sec.deleted_at is null and u.deleted_at is null order by u.first_name asc""",
      nativeQuery = true)
  List<Student> findByGradeAndOrg(List<String> gradeSlug, String orgSlug);

  @Query(
      value =
          """
                      select distinct s.* from subject_profiles_details spd
                      inner join student_subject_profiles ssp on spd.subject_profile_id = ssp.subject_profile_id
                      inner join students s on ssp.student_id = s.id
                      where spd.subject_slug = :subjectSlug and spd.grade_slug = :gradeSlug and spd.board_slug = :boardSlug
                      and s.section_id = :sectionId
                      and s.deleted_at is null""",
      nativeQuery = true)
  List<Student> getStudentsBySubjectProfileAndMlpRequest(
      String gradeSlug, String boardSlug, String subjectSlug, long sectionId);

  @Query(
      value =
          """
                      select s.id  from students s
                      inner join users u on u.id = s.user_id
                      where u.organization in(:orgs)
                      and s.deleted_at is null order by s.id""",
      nativeQuery = true)
  List<Long> getAllStudentsOfOrgs(List<String> orgs);

  @Query(
      value =
          """
          select * from students where id in (:studentIds) and deleted_at is null
          """,
      nativeQuery = true)
  List<Student> findStudentListById(List<Long> studentIds);

  Student findByExtRef(Long customerId);

  List<Student> findByIdInAndSectionAndDeletedAtIsNull(List<Long> studentId, Section section);

  @Query(
      value =
          """
          select  s.* from students s
          inner join users u on u.id = s.user_id
          where u.organization in (:orgSlug) and s.id in(:studentIds)""",
      nativeQuery = true)
  List<Student> findAllByIdsAndOrgSlug(List<String> orgSlug, List<Long> studentIds);

  @Query(
      value =
          """
                  select  s.* from students s
                  inner join users u on u.id = s.user_id
                  where u.organization = :orgSlug
                  and s.deleted_at is null
                  order by s.updated_at desc""",
      nativeQuery = true)
  List<Student> findByOrgSlug(String orgSlug);

  @Query(
      value =
          """
                          select  s.* from students s
                                            inner join users u on u.id = s.user_id
                                            where u.organization = :orgSlug
                                            and u.deleted_at is null and s.fee_defaulter = :feeDefaulter
                                            order by s.updated_at desc
                          """,
      nativeQuery = true)
  List<Student> getFeeUnpaidStudentsByOrg(String orgSlug, boolean feeDefaulter);

  @Query(
      value =
          """
            SELECT s.*
                FROM students s
                WHERE s.id IN (
                  SELECT sav.student_id
                  FROM student_attribute_values sav
                  JOIN student_attribute_definitions sad ON sav.attribute_definition_id = sad.id
                  WHERE sad.org_slug = :orgSlug
                    AND sad.name = 'date_of_birth'
                    AND (
                      (
                        sav.value ~ '^[0-9]{4}-[0-9]{2}-[0-9]{2}$'
                        AND TO_CHAR(TO_DATE(sav.value, 'YYYY-MM-DD'), 'MM-DD') = TO_CHAR(CURRENT_DATE, 'MM-DD')
                      )
                      OR
                      (
                        sav.value ~ '^[0-9]{2}-[0-9]{2}-[0-9]{4}$'
                        AND TO_CHAR(TO_DATE(sav.value, 'DD-MM-YYYY'), 'MM-DD') = TO_CHAR(CURRENT_DATE, 'MM-DD')
                      )
                    )
                )
            """,
      nativeQuery = true)
  List<Student> findBirthdayStudentsByOrgSlug(String orgSlug);

  @Query(
      value =
          """
                          select st.* from  students st
                             inner join sections sec on sec.id = st.section_id
                             where sec.uuid in (:sectionUuids) and organization = :orgSlug and st.deleted_at is null""",
      nativeQuery = true)
  List<Student> getStudentsBySectionUuidsAndOrgSlug(List<UUID> sectionUuids, String orgSlug);

  Optional<Student> findByIdAndClassId(Long prevStudentId, int id);

  @Query(
      value =
          """
                  select u.email as email,to_char(u.created_at , 'YYYY-MM-DD') as createdDate,u.mobile_number as mobileNumber,u.user_name as username, u.first_name as firstName,u.gender as gender,s.school_name as schoolName,
                  s.attributes ->> 'school_pin' as schoolPin,s.attributes ->> 'blood_group' as bloodGroup,
                  s.attributes ->> 'school_city' as schoolCity,s.attributes ->> 'school_state' as schoolState,
                  s.attributes ->> 'principal_name' as principalName,s.attributes ->> 'school_address' as schoolAddress,
                  s.attributes ->> 'date_of_birth' as dateOfBirth,
                  u.last_name as lastName, s.class_id as classId,s.board_id as boardId
                  from students s
                  inner join users u on s.user_id = u.id
                  inner join users p on s.created_by = p.id
                  where u.organization=:organizationSlug
                  """,
      nativeQuery = true)
  List<SkilliomaStudent> findAllStudentsData(String organizationSlug);

  List<Student> getStudentsBySectionAndDeletedAtIsNull(Section section);

  @Query(
      value =
          """
          select distinct s.* from  students s
           inner join classroom_students cs on cs.student_id  = s.id
           inner join classrooms c on c.id = cs.classroom_id
           inner join classroom_teachers ct on ct.classroom_id  = c.id
           where  c.org_slug  = :orgSlug""",
      nativeQuery = true)
  List<Student> getClassroomStudents(String orgSlug);

  @Query(
      value =
          """
                  select distinct s.* from  students s
                   inner join classroom_students cs on cs.student_id  = s.id
                   inner join classrooms c on c.id = cs.classroom_id
                   inner join classroom_teachers ct on ct.classroom_id  = c.id
                   where  (cast((:teacherId) as varChar) is null or ct.teacher_id  =(:teacherId))
                   and c.org_slug  = :orgSlug""",
      nativeQuery = true)
  List<Student> getClassroomStudentByTeacher(Long teacherId, String orgSlug);

  @Query(
      value =
          """
                          select c.name from  students s
                           inner join classroom_students cs on cs.student_id  = s.id
                           inner join classrooms c on c.id = cs.classroom_id
                           inner join classroom_teachers ct on ct.classroom_id  = c.id
                           where  (cast((:teacherId) as varChar) is null or ct.teacher_id  =(:teacherId))
                           and c.org_slug  = :orgSlug and s.id = :studentId""",
      nativeQuery = true)
  List<String> getClassroomByTeacherAndByStudent(Long teacherId, String orgSlug, Long studentId);

  @Query(
      value =
          "select s.* from students s join users u on s.user_id = u.id where u.auth_user_id in (:authUserIds)",
      nativeQuery = true)
  List<Student> getStudentsByUserAuthId(List<String> authUserIds);

  @Query(
      value =
          """
                    select s.* from students s join users u on
                    s.user_id = u.id where s.roll_number =:rollNumber and u.organization =:orgSlug
                    """,
      nativeQuery = true)
  Optional<Student> getStudentByRollNumber(String rollNumber, String orgSlug);

  @Query(
      value =
          "select s.* from students s join users u on s.user_id = u.id where s.roll_number=:rollNumber and u.organization=:orgSlug",
      nativeQuery = true)
  List<Student> getStudentsByRollNumberAndOrg(String rollNumber, String orgSlug);

  @Query(
      value =
          "select s.* from students s join users u on s.user_id = u.id where s.class_roll_number=:rollNumber and u.organization=:orgSlug",
      nativeQuery = true)
  List<Student> getStudentsByClassRollNumberAndOrg(String rollNumber, String orgSlug);

  @Query(
      value =
          """
                  select std.* from students std
                  inner join sections sec on sec.id = std.section_id
                  inner join users u on u.id = std.user_id
                  where sec.grade_slug = :gradeSlug
                    and u.organization = :orgSlug
                    and sec.uuid = CAST(:sectionUuid AS uuid)
                    and std.deleted_at is null
                    and sec.deleted_at is null
                    and u.deleted_at is null""",
      nativeQuery = true)
  List<Student> findByGradeAndSection(String gradeSlug, String orgSlug, String sectionUuid);

  List<Student> findByUserInfoInAndDeletedAtIsNull(List<User> studentUsers);

  @Query(
      value =
          """
                                      select count(s.*) from users u join students s on u.id = s.user_id
                                       where u.organization  = :orgSlug and u.deleted_at is null and s.deleted_at is null
                                  """,
      nativeQuery = true)
  Long getStudentCountByOrg(String orgSlug);

  Optional<Student> findByPrevStudentId(Long prevStudentId);

  @Query(
      value =
          "select s.* from students s join users u on s.user_id = u.id where u.auth_user_id in (:userId) and u.organization=:orgSlug",
      nativeQuery = true)
  List<Student> findAllByUserInfoAndOrgSlug(List<String> userId, String orgSlug);

  @Query(
      value =
          """
                          select s.* from students s
                          join users u on s.user_id = u.id
                          where u.organization =:orgSlug and s.section_id  = :section and s.class_roll_number =:classRollNumber""",
      nativeQuery = true)
  List<Student> getStudentBySectionAndClassRollNumber(
      String orgSlug, Long section, String classRollNumber);

  Optional<Student> findTop1BySection(Section section);

  @Query(
      value =
          """
  select s.* from students s where s.section_id in (:sectionId) and s.id in (:studentIds) and s.deleted_at is null
              """,
      nativeQuery = true)
  List<Student> getByIdsAndSection(List<Long> studentIds, Long sectionId);

  List<Student> findAllByIdInAndDeletedAtIsNull(List<Long> studentIds);

  @Query(
      value =
          """
                  select s.* from users u
                  join students s on s.user_id = u.id
                  where u.external_ref in (:externalRef)
                  """,
      nativeQuery = true)
  List<Student> findStudentByUserExternalRef(List<String> externalRef);

  @Query(
      value =
          """
          select s.section_id  from users u inner join students s on u.id=s.user_id where u.organization = :orgId and  (CONCAT(u.first_name, ' ', u.last_name) ILIKE :searchKey or u.user_name ilike :searchKey)
          """,
      nativeQuery = true)
  List<Long> findSectionIdByMatchingStudent(String orgId, String searchKey);

  @Query(
      value =
          """
                          select st.id as studentId,u.user_name as userName, concat(u.first_name,u.last_name) as studentName,s.grade_name as grade,s."name" as sectionName from students st
                          join users u on u.id = st.user_id
                          join sections s on st.section_id = s.id
                          where (u.user_name ilike :userName or u.first_name ilike :userName or u.last_name ilike :userName) and u.organization = :organization
                          """,
      nativeQuery = true)
  List<StudentInfo> getStudentInfo(String userName, String organization);

  @Query(
      """
    SELECT DISTINCT s
    FROM Student s
    JOIN FETCH s.userInfo u
    JOIN FETCH s.section sec
    WHERE sec.gradeSlug IN :gradeSlugs
      AND u.organization = :orgSlug
    """)
  List<Student> getStudentsByGradeSlugAndOrgSlug(
      @Param("gradeSlugs") List<String> gradeSlugs, @Param("orgSlug") String orgSlug);

  @Query(
      """
  SELECT st FROM Student st
  JOIN FETCH st.userInfo
  WHERE st.section.uuid IN :sectionUuids
    AND st.section.organization = :orgSlug
    AND st.deletedAt IS NULL
    AND (:studentIds IS NULL OR st.id IN :studentIds)
""")
  List<Student> findStudentsBySectionUuidsAndOrgSlugAndStudentIds(
      @Param("sectionUuids") List<UUID> sectionUuids,
      @Param("orgSlug") String orgSlug,
      @Param("studentIds") List<Long> studentIds);
}
