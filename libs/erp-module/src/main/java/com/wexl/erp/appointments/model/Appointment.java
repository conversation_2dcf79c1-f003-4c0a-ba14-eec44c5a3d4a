package com.wexl.erp.appointments.model;

import com.wexl.retail.guardian.model.Guardian;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.model.Student;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "appointments")
public class Appointment {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "guardian_id")
  private Guardian guardian;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "student_id")
  private Student student;

  @Column(name = "org_slug", nullable = false)
  private String orgSlug;

  @Column(name = "appointment_date", nullable = false)
  private LocalDateTime appointmentDate;

  @Column(name = "appointment_reason", nullable = false, columnDefinition = "TEXT")
  private String appointmentReason;

  @Column(name = "role")
  private String role;

  @Column(name = "recipient_name")
  private String recipientName;

  @Enumerated(EnumType.STRING)
  @Column(name = "status", nullable = false)
  private AppointmentStatus status;

  @Column(name = "applied_date", nullable = false)
  private LocalDateTime appliedDate;

  @Column(name = "reviewed_by")
  private String reviewedBy;

  @Column(name = "reviewed_on")
  private LocalDateTime reviewedOn;

  @Enumerated(EnumType.STRING)
  @Column(name = "type", nullable = false)
  private AppointmentType type;

  @Column(name = "attachment", columnDefinition = "TEXT")
  private String attachment;

  @Enumerated(EnumType.ORDINAL)
  @Column(name = "relation_type")
  private GuardianRole relationType;

  @Column(name = "mobile_number")
  private Long mobileNumber;

  @Column(name = "guardian_name")
  private String guardianName;
}
